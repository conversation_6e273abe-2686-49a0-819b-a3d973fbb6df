1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.pasindunaduninduwara.gemma3nchat"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
11-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:5:5-80
11-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:5:22-77
12    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
12-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:6:5-81
12-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:6:22-78
13
14    <permission
14-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
15        android:name="com.pasindunaduninduwara.gemma3nchat.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
15-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
16        android:protectionLevel="signature" />
16-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
17
18    <uses-permission android:name="com.pasindunaduninduwara.gemma3nchat.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
18-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
18-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
19
20    <application
20-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:8:5-26:19
21        android:allowBackup="true"
21-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:9:9-35
22        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
22-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
23        android:dataExtractionRules="@xml/data_extraction_rules"
23-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:10:9-65
24        android:debuggable="true"
25        android:extractNativeLibs="false"
26        android:fullBackupContent="@xml/backup_rules"
26-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:11:9-54
27        android:icon="@mipmap/ic_launcher"
27-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:12:9-43
28        android:label="@string/app_name"
28-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:13:9-41
29        android:roundIcon="@mipmap/ic_launcher_round"
29-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:14:9-54
30        android:supportsRtl="true"
30-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:15:9-35
31        android:theme="@style/Theme.Gemma3nChat" >
31-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:16:9-49
32        <activity
32-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:17:9-25:20
33            android:name="com.pasindunaduninduwara.gemma3nchat.MainActivity"
33-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:18:13-41
34            android:exported="true" >
34-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:19:13-36
35            <intent-filter>
35-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:20:13-24:29
36                <action android:name="android.intent.action.MAIN" />
36-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:21:17-69
36-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:21:25-66
37
38                <category android:name="android.intent.category.LAUNCHER" />
38-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:23:17-77
38-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:23:27-74
39            </intent-filter>
40        </activity>
41
42        <provider
42-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0f05e3af388ffae905ef7244538afe07\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
43            android:name="androidx.startup.InitializationProvider"
43-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0f05e3af388ffae905ef7244538afe07\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
44            android:authorities="com.pasindunaduninduwara.gemma3nchat.androidx-startup"
44-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0f05e3af388ffae905ef7244538afe07\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
45            android:exported="false" >
45-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0f05e3af388ffae905ef7244538afe07\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
46            <meta-data
46-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0f05e3af388ffae905ef7244538afe07\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
47                android:name="androidx.emoji2.text.EmojiCompatInitializer"
47-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0f05e3af388ffae905ef7244538afe07\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
48                android:value="androidx.startup" />
48-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0f05e3af388ffae905ef7244538afe07\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
49            <meta-data
49-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\39b902d69693691bbb7257c74a0b4f7c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
50                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
50-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\39b902d69693691bbb7257c74a0b4f7c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
51                android:value="androidx.startup" />
51-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\39b902d69693691bbb7257c74a0b4f7c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
52            <meta-data
52-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
53                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
53-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
54                android:value="androidx.startup" />
54-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
55        </provider>
56        <!--
57             Applications that target Android S+ require explicit declaration of
58             any referenced vendor-provided libraries.
59        -->
60        <uses-native-library
60-->[com.google.mediapipe:tasks-genai:0.10.24] C:\Users\<USER>\.gradle\caches\8.13\transforms\811ff3037dd9b7d727239d5c8b219f87\transformed\tasks-genai-0.10.24\AndroidManifest.xml:15:9-17:40
61            android:name="libOpenCL.so"
61-->[com.google.mediapipe:tasks-genai:0.10.24] C:\Users\<USER>\.gradle\caches\8.13\transforms\811ff3037dd9b7d727239d5c8b219f87\transformed\tasks-genai-0.10.24\AndroidManifest.xml:16:13-40
62            android:required="false" />
62-->[com.google.mediapipe:tasks-genai:0.10.24] C:\Users\<USER>\.gradle\caches\8.13\transforms\811ff3037dd9b7d727239d5c8b219f87\transformed\tasks-genai-0.10.24\AndroidManifest.xml:17:13-37
63        <uses-native-library
63-->[com.google.mediapipe:tasks-genai:0.10.24] C:\Users\<USER>\.gradle\caches\8.13\transforms\811ff3037dd9b7d727239d5c8b219f87\transformed\tasks-genai-0.10.24\AndroidManifest.xml:15:9-17:40
64            android:name="libOpenCL-car.so"
64-->[com.google.mediapipe:tasks-genai:0.10.24] C:\Users\<USER>\.gradle\caches\8.13\transforms\811ff3037dd9b7d727239d5c8b219f87\transformed\tasks-genai-0.10.24\AndroidManifest.xml:16:13-40
65            android:required="false" />
65-->[com.google.mediapipe:tasks-genai:0.10.24] C:\Users\<USER>\.gradle\caches\8.13\transforms\811ff3037dd9b7d727239d5c8b219f87\transformed\tasks-genai-0.10.24\AndroidManifest.xml:17:13-37
66        <uses-native-library
66-->[com.google.mediapipe:tasks-genai:0.10.24] C:\Users\<USER>\.gradle\caches\8.13\transforms\811ff3037dd9b7d727239d5c8b219f87\transformed\tasks-genai-0.10.24\AndroidManifest.xml:15:9-17:40
67            android:name="libOpenCL-pixel.so"
67-->[com.google.mediapipe:tasks-genai:0.10.24] C:\Users\<USER>\.gradle\caches\8.13\transforms\811ff3037dd9b7d727239d5c8b219f87\transformed\tasks-genai-0.10.24\AndroidManifest.xml:16:13-40
68            android:required="false" />
68-->[com.google.mediapipe:tasks-genai:0.10.24] C:\Users\<USER>\.gradle\caches\8.13\transforms\811ff3037dd9b7d727239d5c8b219f87\transformed\tasks-genai-0.10.24\AndroidManifest.xml:17:13-37
69
70        <receiver
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
71            android:name="androidx.profileinstaller.ProfileInstallReceiver"
71-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
72            android:directBootAware="false"
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
73            android:enabled="true"
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
74            android:exported="true"
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
75            android:permission="android.permission.DUMP" >
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
76            <intent-filter>
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
77                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
78            </intent-filter>
79            <intent-filter>
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
80                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
81            </intent-filter>
82            <intent-filter>
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
83                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
84            </intent-filter>
85            <intent-filter>
85-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
86                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
86-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
86-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
87            </intent-filter>
88        </receiver>
89    </application>
90
91</manifest>
