1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.pasindunaduninduwara.gemma3nchat"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <permission
11-->[androidx.core:core:1.10.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
12        android:name="com.pasindunaduninduwara.gemma3nchat.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.10.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.10.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.pasindunaduninduwara.gemma3nchat.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.10.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.10.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
16
17    <application
17-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:5:5-23:19
18        android:allowBackup="true"
18-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.10.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:7:9-65
21        android:debuggable="true"
22        android:extractNativeLibs="false"
23        android:fullBackupContent="@xml/backup_rules"
23-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:8:9-54
24        android:icon="@mipmap/ic_launcher"
24-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:9:9-43
25        android:label="@string/app_name"
25-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:10:9-41
26        android:roundIcon="@mipmap/ic_launcher_round"
26-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:11:9-54
27        android:supportsRtl="true"
27-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:12:9-35
28        android:testOnly="true"
29        android:theme="@style/Theme.Gemma3nChat" >
29-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:13:9-49
30        <activity
30-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:14:9-22:20
31            android:name="com.pasindunaduninduwara.gemma3nchat.MainActivity"
31-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:15:13-41
32            android:exported="true" >
32-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:16:13-36
33            <intent-filter>
33-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:17:13-21:29
34                <action android:name="android.intent.action.MAIN" />
34-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:18:17-69
34-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:18:25-66
35
36                <category android:name="android.intent.category.LAUNCHER" />
36-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:20:17-77
36-->C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:20:27-74
37            </intent-filter>
38        </activity>
39
40        <provider
40-->[androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\0f05e3af388ffae905ef7244538afe07\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
41            android:name="androidx.startup.InitializationProvider"
41-->[androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\0f05e3af388ffae905ef7244538afe07\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
42            android:authorities="com.pasindunaduninduwara.gemma3nchat.androidx-startup"
42-->[androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\0f05e3af388ffae905ef7244538afe07\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
43            android:exported="false" >
43-->[androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\0f05e3af388ffae905ef7244538afe07\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
44            <meta-data
44-->[androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\0f05e3af388ffae905ef7244538afe07\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
45                android:name="androidx.emoji2.text.EmojiCompatInitializer"
45-->[androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\0f05e3af388ffae905ef7244538afe07\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
46                android:value="androidx.startup" />
46-->[androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\0f05e3af388ffae905ef7244538afe07\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
47            <meta-data
47-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\39b902d69693691bbb7257c74a0b4f7c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
48                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
48-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\39b902d69693691bbb7257c74a0b4f7c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
49                android:value="androidx.startup" />
49-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\39b902d69693691bbb7257c74a0b4f7c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
50            <meta-data
50-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
51                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
51-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
52                android:value="androidx.startup" />
52-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
53        </provider>
54        <!--
55             Applications that target Android S+ require explicit declaration of
56             any referenced vendor-provided libraries.
57        -->
58        <uses-native-library
58-->[com.google.mediapipe:tasks-genai:0.10.24] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\811ff3037dd9b7d727239d5c8b219f87\transformed\tasks-genai-0.10.24\AndroidManifest.xml:15:9-17:40
59            android:name="libOpenCL.so"
59-->[com.google.mediapipe:tasks-genai:0.10.24] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\811ff3037dd9b7d727239d5c8b219f87\transformed\tasks-genai-0.10.24\AndroidManifest.xml:16:13-40
60            android:required="false" />
60-->[com.google.mediapipe:tasks-genai:0.10.24] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\811ff3037dd9b7d727239d5c8b219f87\transformed\tasks-genai-0.10.24\AndroidManifest.xml:17:13-37
61        <uses-native-library
61-->[com.google.mediapipe:tasks-genai:0.10.24] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\811ff3037dd9b7d727239d5c8b219f87\transformed\tasks-genai-0.10.24\AndroidManifest.xml:15:9-17:40
62            android:name="libOpenCL-car.so"
62-->[com.google.mediapipe:tasks-genai:0.10.24] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\811ff3037dd9b7d727239d5c8b219f87\transformed\tasks-genai-0.10.24\AndroidManifest.xml:16:13-40
63            android:required="false" />
63-->[com.google.mediapipe:tasks-genai:0.10.24] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\811ff3037dd9b7d727239d5c8b219f87\transformed\tasks-genai-0.10.24\AndroidManifest.xml:17:13-37
64        <uses-native-library
64-->[com.google.mediapipe:tasks-genai:0.10.24] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\811ff3037dd9b7d727239d5c8b219f87\transformed\tasks-genai-0.10.24\AndroidManifest.xml:15:9-17:40
65            android:name="libOpenCL-pixel.so"
65-->[com.google.mediapipe:tasks-genai:0.10.24] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\811ff3037dd9b7d727239d5c8b219f87\transformed\tasks-genai-0.10.24\AndroidManifest.xml:16:13-40
66            android:required="false" />
66-->[com.google.mediapipe:tasks-genai:0.10.24] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\811ff3037dd9b7d727239d5c8b219f87\transformed\tasks-genai-0.10.24\AndroidManifest.xml:17:13-37
67
68        <receiver
68-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
69            android:name="androidx.profileinstaller.ProfileInstallReceiver"
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
70            android:directBootAware="false"
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
71            android:enabled="true"
71-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
72            android:exported="true"
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
73            android:permission="android.permission.DUMP" >
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
74            <intent-filter>
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
75                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
76            </intent-filter>
77            <intent-filter>
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
78                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
79            </intent-filter>
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
81                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
82            </intent-filter>
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
84                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
85            </intent-filter>
86        </receiver>
87    </application>
88
89</manifest>
