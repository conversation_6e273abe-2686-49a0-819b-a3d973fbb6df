-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:2:1-25:12
INJECTED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:2:1-25:12
INJECTED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:2:1-25:12
INJECTED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:2:1-25:12
MERGED from [com.google.android.material:material:1.10.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\88230c86a3b262199795a323af13815e\transformed\material-1.10.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\d235b2626c25088bcd582fdddf1dd2ba\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\71e887551d42e6b914c06b4a697abd06\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\9f5a346deabef2d264d3bedfff450c48\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\ee3b8d626a3f96127d1a04b8d788b834\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\5cae38b15bf6e8792aedd3ae62c5922e\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\8e5f7444eabe20a5dd2b595f66533f55\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\80f9f485394cb076d6d165bf3a5db1de\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\0f05e3af388ffae905ef7244538afe07\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\dc91ef699284925adcc52e59f7f22413\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\2bbb9b127d682bbce77d1cfb00769656\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\95e28efaa1770878afca105ac1115908\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4f60c1136a78a8f68d11829cd7d353a1\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\0df14d914099e718f7f7202717fe9e3c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\ca68329f6b29b21df0ce9821b62ada80\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\60a83b5016ea482380c493d50b06d79a\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\670043169081a3346798810faacf2c26\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\ac06763a736a248a4eb6b1c4f0b43b41\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\2e73f31c9796f777eee2f57a163809c3\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\7539f8aeef4eaf26eb2619611a904d64\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.10.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\760a5b6119289b33bf94febfdfbd9301\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\cc92086cb7c33ac3231f261f76c33543\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\39b902d69693691bbb7257c74a0b4f7c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\10d8910e438d7e51d54dea6b37f8ac6e\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\7ec9049daa8a21354adc0c5b508d951a\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\a12e93ad53075578c663df579f374b63\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\91021afe5a9744708a65383c71d125c3\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.10.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\547bac58042b8f6ef605f1067fa14588\transformed\core-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\c02aacb3f57bf1a24fecdb75eb3f0d14\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.mediapipe:tasks-genai:0.10.24] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\811ff3037dd9b7d727239d5c8b219f87\transformed\tasks-genai-0.10.24\AndroidManifest.xml:2:1-26:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\728534b77681dd7f3b9c2cf44dd60bee\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\04a67efc693079b870970eee3271495a\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\76fbf22e4ce391fc279e66b41a3a3a59\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\35a477ae72bf500ffecd7f70f6dee7e8\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\b12e0359abde382d3cf9f8e7467ef50e\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\9a6151dde71ab401c9fe849756b7baf6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\e70fd7456d3a54dca4d6e5e6aeea4083\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\29e50316bb249c134d0728d3a18f1201\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\73927ecfa662227f62da4faa9e9cdfae\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\366cfc3e64478f1fa5c4e546bbaf4db7\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:5:5-23:19
INJECTED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:5:5-23:19
MERGED from [com.google.android.material:material:1.10.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\88230c86a3b262199795a323af13815e\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.10.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\88230c86a3b262199795a323af13815e\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\d235b2626c25088bcd582fdddf1dd2ba\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\d235b2626c25088bcd582fdddf1dd2ba\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\0f05e3af388ffae905ef7244538afe07\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\0f05e3af388ffae905ef7244538afe07\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.10.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.10.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\39b902d69693691bbb7257c74a0b4f7c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\39b902d69693691bbb7257c74a0b4f7c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.mediapipe:tasks-genai:0.10.24] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\811ff3037dd9b7d727239d5c8b219f87\transformed\tasks-genai-0.10.24\AndroidManifest.xml:9:5-24:19
MERGED from [com.google.mediapipe:tasks-genai:0.10.24] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\811ff3037dd9b7d727239d5c8b219f87\transformed\tasks-genai-0.10.24\AndroidManifest.xml:9:5-24:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\76fbf22e4ce391fc279e66b41a3a3a59\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\76fbf22e4ce391fc279e66b41a3a3a59\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\9a6151dde71ab401c9fe849756b7baf6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\9a6151dde71ab401c9fe849756b7baf6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.10.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:12:9-35
	android:label
		ADDED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:10:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:8:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:11:9-54
	android:icon
		ADDED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:9:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:6:9-35
	android:theme
		ADDED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:13:9-49
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:7:9-65
activity#com.pasindunaduninduwara.gemma3nchat.MainActivity
ADDED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:14:9-22:20
	android:exported
		ADDED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:16:13-36
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:15:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:17:13-21:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:18:17-69
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:18:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:20:17-77
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml:20:27-74
uses-sdk
INJECTED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.10.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\88230c86a3b262199795a323af13815e\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\88230c86a3b262199795a323af13815e\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\d235b2626c25088bcd582fdddf1dd2ba\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\d235b2626c25088bcd582fdddf1dd2ba\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\71e887551d42e6b914c06b4a697abd06\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\71e887551d42e6b914c06b4a697abd06\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\9f5a346deabef2d264d3bedfff450c48\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\9f5a346deabef2d264d3bedfff450c48\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\ee3b8d626a3f96127d1a04b8d788b834\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\ee3b8d626a3f96127d1a04b8d788b834\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\5cae38b15bf6e8792aedd3ae62c5922e\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\5cae38b15bf6e8792aedd3ae62c5922e\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\8e5f7444eabe20a5dd2b595f66533f55\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\8e5f7444eabe20a5dd2b595f66533f55\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\80f9f485394cb076d6d165bf3a5db1de\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\80f9f485394cb076d6d165bf3a5db1de\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\0f05e3af388ffae905ef7244538afe07\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\0f05e3af388ffae905ef7244538afe07\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\dc91ef699284925adcc52e59f7f22413\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\dc91ef699284925adcc52e59f7f22413\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\2bbb9b127d682bbce77d1cfb00769656\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\2bbb9b127d682bbce77d1cfb00769656\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\95e28efaa1770878afca105ac1115908\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\95e28efaa1770878afca105ac1115908\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4f60c1136a78a8f68d11829cd7d353a1\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4f60c1136a78a8f68d11829cd7d353a1\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\0df14d914099e718f7f7202717fe9e3c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\0df14d914099e718f7f7202717fe9e3c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\ca68329f6b29b21df0ce9821b62ada80\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\ca68329f6b29b21df0ce9821b62ada80\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\60a83b5016ea482380c493d50b06d79a\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\60a83b5016ea482380c493d50b06d79a\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\670043169081a3346798810faacf2c26\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\670043169081a3346798810faacf2c26\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\ac06763a736a248a4eb6b1c4f0b43b41\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\ac06763a736a248a4eb6b1c4f0b43b41\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\2e73f31c9796f777eee2f57a163809c3\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\2e73f31c9796f777eee2f57a163809c3\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\7539f8aeef4eaf26eb2619611a904d64\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\7539f8aeef4eaf26eb2619611a904d64\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.10.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.10.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\760a5b6119289b33bf94febfdfbd9301\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\760a5b6119289b33bf94febfdfbd9301\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\cc92086cb7c33ac3231f261f76c33543\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\cc92086cb7c33ac3231f261f76c33543\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\39b902d69693691bbb7257c74a0b4f7c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\39b902d69693691bbb7257c74a0b4f7c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\10d8910e438d7e51d54dea6b37f8ac6e\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\10d8910e438d7e51d54dea6b37f8ac6e\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\7ec9049daa8a21354adc0c5b508d951a\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\7ec9049daa8a21354adc0c5b508d951a\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\a12e93ad53075578c663df579f374b63\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\a12e93ad53075578c663df579f374b63\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\91021afe5a9744708a65383c71d125c3\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\91021afe5a9744708a65383c71d125c3\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.10.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\547bac58042b8f6ef605f1067fa14588\transformed\core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.10.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\547bac58042b8f6ef605f1067fa14588\transformed\core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\c02aacb3f57bf1a24fecdb75eb3f0d14\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\c02aacb3f57bf1a24fecdb75eb3f0d14\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.mediapipe:tasks-genai:0.10.24] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\811ff3037dd9b7d727239d5c8b219f87\transformed\tasks-genai-0.10.24\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.mediapipe:tasks-genai:0.10.24] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\811ff3037dd9b7d727239d5c8b219f87\transformed\tasks-genai-0.10.24\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\728534b77681dd7f3b9c2cf44dd60bee\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\728534b77681dd7f3b9c2cf44dd60bee\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\04a67efc693079b870970eee3271495a\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\04a67efc693079b870970eee3271495a\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\76fbf22e4ce391fc279e66b41a3a3a59\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\76fbf22e4ce391fc279e66b41a3a3a59\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\35a477ae72bf500ffecd7f70f6dee7e8\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\35a477ae72bf500ffecd7f70f6dee7e8\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\b12e0359abde382d3cf9f8e7467ef50e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\b12e0359abde382d3cf9f8e7467ef50e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\9a6151dde71ab401c9fe849756b7baf6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\9a6151dde71ab401c9fe849756b7baf6\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\e70fd7456d3a54dca4d6e5e6aeea4083\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\e70fd7456d3a54dca4d6e5e6aeea4083\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\29e50316bb249c134d0728d3a18f1201\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\29e50316bb249c134d0728d3a18f1201\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\73927ecfa662227f62da4faa9e9cdfae\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\73927ecfa662227f62da4faa9e9cdfae\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\366cfc3e64478f1fa5c4e546bbaf4db7\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\366cfc3e64478f1fa5c4e546bbaf4db7\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\StudioProjects\gemma3nChat\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\0f05e3af388ffae905ef7244538afe07\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\39b902d69693691bbb7257c74a0b4f7c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\39b902d69693691bbb7257c74a0b4f7c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\76fbf22e4ce391fc279e66b41a3a3a59\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\76fbf22e4ce391fc279e66b41a3a3a59\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\0f05e3af388ffae905ef7244538afe07\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\0f05e3af388ffae905ef7244538afe07\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\0f05e3af388ffae905ef7244538afe07\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\0f05e3af388ffae905ef7244538afe07\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\0f05e3af388ffae905ef7244538afe07\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\0f05e3af388ffae905ef7244538afe07\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\0f05e3af388ffae905ef7244538afe07\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
permission#com.pasindunaduninduwara.gemma3nchat.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
uses-permission#com.pasindunaduninduwara.gemma3nchat.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\bad0ae0ea2c1a00885fb1b4923a808de\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\39b902d69693691bbb7257c74a0b4f7c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\39b902d69693691bbb7257c74a0b4f7c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\39b902d69693691bbb7257c74a0b4f7c\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
uses-native-library
ADDED from [com.google.mediapipe:tasks-genai:0.10.24] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\811ff3037dd9b7d727239d5c8b219f87\transformed\tasks-genai-0.10.24\AndroidManifest.xml:15:9-17:40
	android:required
		ADDED from [com.google.mediapipe:tasks-genai:0.10.24] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\811ff3037dd9b7d727239d5c8b219f87\transformed\tasks-genai-0.10.24\AndroidManifest.xml:17:13-37
	android:name
		ADDED from [com.google.mediapipe:tasks-genai:0.10.24] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\811ff3037dd9b7d727239d5c8b219f87\transformed\tasks-genai-0.10.24\AndroidManifest.xml:16:13-40
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Gradle\gradle-8.14.2\bin\caches\8.13\transforms\4656548bcf0ca1d906d57bdc5c81ddf1\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
