  Dispatchers android.app.Activity  	Exception android.app.Activity  File android.app.Activity  LlmInference android.app.Activity  R android.app.Activity  View android.app.Activity  initializeLlmInference android.app.Activity  
inputEditText android.app.Activity  
isNotEmpty android.app.Activity  launch android.app.Activity  lifecycleScope android.app.Activity  llmInference android.app.Activity  	modelPath android.app.Activity  onCreate android.app.Activity  progressBar android.app.Activity  responseTextView android.app.Activity  
sendButton android.app.Activity  trim android.app.Activity  withContext android.app.Activity  Dispatchers android.content.Context  	Exception android.content.Context  File android.content.Context  LlmInference android.content.Context  R android.content.Context  View android.content.Context  initializeLlmInference android.content.Context  
inputEditText android.content.Context  
isNotEmpty android.content.Context  launch android.content.Context  lifecycleScope android.content.Context  llmInference android.content.Context  	modelPath android.content.Context  progressBar android.content.Context  responseTextView android.content.Context  
sendButton android.content.Context  trim android.content.Context  withContext android.content.Context  Dispatchers android.content.ContextWrapper  	Exception android.content.ContextWrapper  File android.content.ContextWrapper  LlmInference android.content.ContextWrapper  R android.content.ContextWrapper  View android.content.ContextWrapper  initializeLlmInference android.content.ContextWrapper  
inputEditText android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  launch android.content.ContextWrapper  lifecycleScope android.content.ContextWrapper  llmInference android.content.ContextWrapper  	modelPath android.content.ContextWrapper  progressBar android.content.ContextWrapper  responseTextView android.content.ContextWrapper  
sendButton android.content.ContextWrapper  trim android.content.ContextWrapper  withContext android.content.ContextWrapper  Bundle 
android.os  clear android.text.Editable  View android.view  Dispatchers  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  File  android.view.ContextThemeWrapper  LlmInference  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  initializeLlmInference  android.view.ContextThemeWrapper  
inputEditText  android.view.ContextThemeWrapper  
isNotEmpty  android.view.ContextThemeWrapper  launch  android.view.ContextThemeWrapper  lifecycleScope  android.view.ContextThemeWrapper  llmInference  android.view.ContextThemeWrapper  	modelPath  android.view.ContextThemeWrapper  progressBar  android.view.ContextThemeWrapper  responseTextView  android.view.ContextThemeWrapper  
sendButton  android.view.ContextThemeWrapper  trim  android.view.ContextThemeWrapper  withContext  android.view.ContextThemeWrapper  GONE android.view.View  OnClickListener android.view.View  VISIBLE android.view.View  	isEnabled android.view.View  setOnClickListener android.view.View  
visibility android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  Button android.widget  EditText android.widget  ProgressBar android.widget  TextView android.widget  	isEnabled android.widget.Button  setOnClickListener android.widget.Button  	isEnabled android.widget.EditText  text android.widget.EditText  
visibility android.widget.ProgressBar  text android.widget.TextView  Dispatchers #androidx.activity.ComponentActivity  	Exception #androidx.activity.ComponentActivity  File #androidx.activity.ComponentActivity  LlmInference #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  initializeLlmInference #androidx.activity.ComponentActivity  
inputEditText #androidx.activity.ComponentActivity  
isNotEmpty #androidx.activity.ComponentActivity  launch #androidx.activity.ComponentActivity  lifecycleScope #androidx.activity.ComponentActivity  llmInference #androidx.activity.ComponentActivity  	modelPath #androidx.activity.ComponentActivity  progressBar #androidx.activity.ComponentActivity  responseTextView #androidx.activity.ComponentActivity  
sendButton #androidx.activity.ComponentActivity  trim #androidx.activity.ComponentActivity  withContext #androidx.activity.ComponentActivity  AppCompatActivity androidx.appcompat.app  Dispatchers (androidx.appcompat.app.AppCompatActivity  	Exception (androidx.appcompat.app.AppCompatActivity  File (androidx.appcompat.app.AppCompatActivity  LlmInference (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  View (androidx.appcompat.app.AppCompatActivity  findViewById (androidx.appcompat.app.AppCompatActivity  initializeLlmInference (androidx.appcompat.app.AppCompatActivity  
inputEditText (androidx.appcompat.app.AppCompatActivity  
isNotEmpty (androidx.appcompat.app.AppCompatActivity  launch (androidx.appcompat.app.AppCompatActivity  lifecycleScope (androidx.appcompat.app.AppCompatActivity  llmInference (androidx.appcompat.app.AppCompatActivity  	modelPath (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  progressBar (androidx.appcompat.app.AppCompatActivity  responseTextView (androidx.appcompat.app.AppCompatActivity  
sendButton (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  trim (androidx.appcompat.app.AppCompatActivity  withContext (androidx.appcompat.app.AppCompatActivity  Dispatchers #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  File #androidx.core.app.ComponentActivity  LlmInference #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  initializeLlmInference #androidx.core.app.ComponentActivity  
inputEditText #androidx.core.app.ComponentActivity  
isNotEmpty #androidx.core.app.ComponentActivity  launch #androidx.core.app.ComponentActivity  lifecycleScope #androidx.core.app.ComponentActivity  llmInference #androidx.core.app.ComponentActivity  	modelPath #androidx.core.app.ComponentActivity  progressBar #androidx.core.app.ComponentActivity  responseTextView #androidx.core.app.ComponentActivity  
sendButton #androidx.core.app.ComponentActivity  trim #androidx.core.app.ComponentActivity  withContext #androidx.core.app.ComponentActivity  Dispatchers &androidx.fragment.app.FragmentActivity  	Exception &androidx.fragment.app.FragmentActivity  File &androidx.fragment.app.FragmentActivity  LlmInference &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  View &androidx.fragment.app.FragmentActivity  initializeLlmInference &androidx.fragment.app.FragmentActivity  
inputEditText &androidx.fragment.app.FragmentActivity  
isNotEmpty &androidx.fragment.app.FragmentActivity  launch &androidx.fragment.app.FragmentActivity  lifecycleScope &androidx.fragment.app.FragmentActivity  llmInference &androidx.fragment.app.FragmentActivity  	modelPath &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  progressBar &androidx.fragment.app.FragmentActivity  responseTextView &androidx.fragment.app.FragmentActivity  
sendButton &androidx.fragment.app.FragmentActivity  trim &androidx.fragment.app.FragmentActivity  withContext &androidx.fragment.app.FragmentActivity  LifecycleCoroutineScope androidx.lifecycle  lifecycleScope androidx.lifecycle  launch *androidx.lifecycle.LifecycleCoroutineScope  LlmInference -com.google.mediapipe.tasks.genai.llminference  createFromOptions :com.google.mediapipe.tasks.genai.llminference.LlmInference  generateResponse :com.google.mediapipe.tasks.genai.llminference.LlmInference  builder Ncom.google.mediapipe.tasks.genai.llminference.LlmInference.LlmInferenceOptions  build Vcom.google.mediapipe.tasks.genai.llminference.LlmInference.LlmInferenceOptions.Builder  setMaxTokens Vcom.google.mediapipe.tasks.genai.llminference.LlmInference.LlmInferenceOptions.Builder  setModelPath Vcom.google.mediapipe.tasks.genai.llminference.LlmInference.LlmInferenceOptions.Builder  AppCompatActivity $com.pasindunaduninduwara.gemma3nchat  Bundle $com.pasindunaduninduwara.gemma3nchat  Button $com.pasindunaduninduwara.gemma3nchat  Dispatchers $com.pasindunaduninduwara.gemma3nchat  EditText $com.pasindunaduninduwara.gemma3nchat  	Exception $com.pasindunaduninduwara.gemma3nchat  File $com.pasindunaduninduwara.gemma3nchat  LlmInference $com.pasindunaduninduwara.gemma3nchat  MainActivity $com.pasindunaduninduwara.gemma3nchat  ProgressBar $com.pasindunaduninduwara.gemma3nchat  R $com.pasindunaduninduwara.gemma3nchat  String $com.pasindunaduninduwara.gemma3nchat  TextView $com.pasindunaduninduwara.gemma3nchat  View $com.pasindunaduninduwara.gemma3nchat  initializeLlmInference $com.pasindunaduninduwara.gemma3nchat  
inputEditText $com.pasindunaduninduwara.gemma3nchat  
isNotEmpty $com.pasindunaduninduwara.gemma3nchat  launch $com.pasindunaduninduwara.gemma3nchat  llmInference $com.pasindunaduninduwara.gemma3nchat  	modelPath $com.pasindunaduninduwara.gemma3nchat  progressBar $com.pasindunaduninduwara.gemma3nchat  responseTextView $com.pasindunaduninduwara.gemma3nchat  
sendButton $com.pasindunaduninduwara.gemma3nchat  trim $com.pasindunaduninduwara.gemma3nchat  withContext $com.pasindunaduninduwara.gemma3nchat  Dispatchers 1com.pasindunaduninduwara.gemma3nchat.MainActivity  	Exception 1com.pasindunaduninduwara.gemma3nchat.MainActivity  File 1com.pasindunaduninduwara.gemma3nchat.MainActivity  LlmInference 1com.pasindunaduninduwara.gemma3nchat.MainActivity  R 1com.pasindunaduninduwara.gemma3nchat.MainActivity  View 1com.pasindunaduninduwara.gemma3nchat.MainActivity  findViewById 1com.pasindunaduninduwara.gemma3nchat.MainActivity  generateResponse 1com.pasindunaduninduwara.gemma3nchat.MainActivity  initializeLlmInference 1com.pasindunaduninduwara.gemma3nchat.MainActivity  
inputEditText 1com.pasindunaduninduwara.gemma3nchat.MainActivity  
isNotEmpty 1com.pasindunaduninduwara.gemma3nchat.MainActivity  launch 1com.pasindunaduninduwara.gemma3nchat.MainActivity  lifecycleScope 1com.pasindunaduninduwara.gemma3nchat.MainActivity  llmInference 1com.pasindunaduninduwara.gemma3nchat.MainActivity  	modelName 1com.pasindunaduninduwara.gemma3nchat.MainActivity  	modelPath 1com.pasindunaduninduwara.gemma3nchat.MainActivity  progressBar 1com.pasindunaduninduwara.gemma3nchat.MainActivity  responseTextView 1com.pasindunaduninduwara.gemma3nchat.MainActivity  
sendButton 1com.pasindunaduninduwara.gemma3nchat.MainActivity  setContentView 1com.pasindunaduninduwara.gemma3nchat.MainActivity  trim 1com.pasindunaduninduwara.gemma3nchat.MainActivity  withContext 1com.pasindunaduninduwara.gemma3nchat.MainActivity  
inputEditText )com.pasindunaduninduwara.gemma3nchat.R.id  progressBar )com.pasindunaduninduwara.gemma3nchat.R.id  responseTextView )com.pasindunaduninduwara.gemma3nchat.R.id  
sendButton )com.pasindunaduninduwara.gemma3nchat.R.id  
activity_main -com.pasindunaduninduwara.gemma3nchat.R.layout  File java.io  exists java.io.File  	Exception 	java.lang  message java.lang.Exception  printStackTrace java.lang.Exception  CharSequence kotlin  	Function1 kotlin  Nothing kotlin  toString 
kotlin.Any  not kotlin.Boolean  
isNotEmpty 
kotlin.String  trim 
kotlin.String  message kotlin.Throwable  printStackTrace kotlin.Throwable  
isNotEmpty kotlin.collections  SuspendFunction1 kotlin.coroutines  
isNotEmpty kotlin.text  trim kotlin.text  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  Dispatchers !kotlinx.coroutines.CoroutineScope  	Exception !kotlinx.coroutines.CoroutineScope  File !kotlinx.coroutines.CoroutineScope  LlmInference !kotlinx.coroutines.CoroutineScope  View !kotlinx.coroutines.CoroutineScope  initializeLlmInference !kotlinx.coroutines.CoroutineScope  
inputEditText !kotlinx.coroutines.CoroutineScope  llmInference !kotlinx.coroutines.CoroutineScope  	modelPath !kotlinx.coroutines.CoroutineScope  progressBar !kotlinx.coroutines.CoroutineScope  responseTextView !kotlinx.coroutines.CoroutineScope  
sendButton !kotlinx.coroutines.CoroutineScope  withContext !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  Main kotlinx.coroutines.Dispatchers                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 