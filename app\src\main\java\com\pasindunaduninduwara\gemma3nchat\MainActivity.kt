package com.pasindunaduninduwara.gemma3nchat

import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.ProgressBar
import android.widget.TextView
import androidx.lifecycle.lifecycleScope
import com.google.mediapipe.tasks.genai.llminference.LlmInference
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

class MainActivity : AppCompatActivity() {

    private lateinit var llmInference: LlmInference
    private lateinit var inputEditText: EditText
    private lateinit var sendButton: Button
    private lateinit var responseTextView: TextView
    private lateinit var progressBar: ProgressBar

    private val modelName = "gemma-3n-E2B-it-int4.task"
    private val modelPath = "/data/local/tmp/llm/$modelName"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        // Initialize UI components
        inputEditText = findViewById(R.id.inputEditText)
        sendButton = findViewById(R.id.sendButton)
        responseTextView = findViewById(R.id.responseTextView)
        progressBar = findViewById(R.id.progressBar)

        // Initialize the model in the background
        progressBar.visibility = View.VISIBLE
        lifecycleScope.launch {
            try {
                initializeLlmInference()
                // Enable button and show a message when model is ready
                sendButton.isEnabled = true
                progressBar.visibility = View.GONE
                responseTextView.text = "Model is ready. Ask me anything!"
            } catch (e: Exception) {
                progressBar.visibility = View.GONE
                responseTextView.text = "Error initializing model: ${e.message}"
                e.printStackTrace()
            }
        }

        sendButton.setOnClickListener {
            val inputText = inputEditText.text.toString().trim()
            if (inputText.isNotEmpty()) {
                generateResponse(inputText)
            }
        }
    }

    private suspend fun initializeLlmInference() {
        // Use withContext to run this blocking call on a background thread
        withContext(Dispatchers.IO) {
            // Check if model file exists
            val modelFile = File(modelPath)
            if (!modelFile.exists()) {
                throw Exception("Model file not found at: $modelPath\n\nPlease ensure you have:\n1. Downloaded the model file\n2. Pushed it to the device using: adb push <model_file> $modelPath")
            }

            val options = LlmInference.LlmInferenceOptions.builder()
                .setModelPath(modelPath)
                .setMaxTokens(1000)
                .build()
            llmInference = LlmInference.createFromOptions(this@MainActivity, options)
        }
    }

    private fun generateResponse(prompt: String) {
        responseTextView.text = "Thinking..."
        inputEditText.isEnabled = false
        sendButton.isEnabled = false
        progressBar.visibility = View.VISIBLE

        // Use a coroutine to run the inference on a background thread
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val fullPrompt = "USER: $prompt\nMODEL:"
                val response = llmInference.generateResponse(fullPrompt)

                // Switch back to the main thread to update the UI
                withContext(Dispatchers.Main) {
                    responseTextView.text = response
                    inputEditText.text.clear() // Clear input field
                    inputEditText.isEnabled = true
                    sendButton.isEnabled = true
                    progressBar.visibility = View.GONE
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    responseTextView.text = "Error: ${e.message}"
                    inputEditText.isEnabled = true
                    sendButton.isEnabled = true
                    progressBar.visibility = View.GONE
                }
                e.printStackTrace()
            }
        }
    }
}